<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TinyMCE 全屏模式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .test-steps {
            background: #f3e5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #9c27b0;
        }
        
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        
        /* TinyMCE 全屏模式修复样式 */
        .tox-fullscreen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 10000 !important;
            background: #fff !important;
            display: flex !important;
            flex-direction: column !important;
        }
        
        .tox-fullscreen .tox-editor-header {
            flex-shrink: 0 !important;
            background: #f4f4f4 !important;
            border-bottom: 1px solid #ccc !important;
        }
        
        .tox-fullscreen .tox-menubar,
        .tox-fullscreen .tox-toolbar {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
            background: #f4f4f4 !important;
            border-bottom: 1px solid #ddd !important;
        }
        
        .tox-fullscreen .tox-edit-area {
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
        }
        
        .tox-fullscreen .tox-edit-area iframe {
            flex: 1 !important;
            width: 100% !important;
            height: 100% !important;
        }
        
        .tox-fullscreen .tox-statusbar {
            flex-shrink: 0 !important;
            background: #f4f4f4 !important;
            border-top: 1px solid #ccc !important;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 TinyMCE 全屏模式测试</h1>
        
        <div class="test-info">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试 TinyMCE 5.4.1 全屏模式下工具栏和菜单栏的显示问题修复效果。</p>
        </div>
        
        <div class="test-steps">
            <h3>🔍 测试步骤</h3>
            <ol>
                <li>点击编辑器工具栏中的 <strong>全屏</strong> 按钮（📺 图标）</li>
                <li>检查全屏模式下是否能看到：
                    <ul>
                        <li>顶部菜单栏（文件、编辑、视图等）</li>
                        <li>工具栏（加粗、斜体、对齐等按钮）</li>
                        <li>底部状态栏（字数统计等）</li>
                    </ul>
                </li>
                <li>尝试使用工具栏功能（如加粗、斜体等）</li>
                <li>按 ESC 键或点击全屏按钮退出全屏模式</li>
            </ol>
        </div>
        
        <h3>📝 编辑器测试区域</h3>
        <textarea id="tinymce-test">
            <h2>欢迎使用 TinyMCE 编辑器</h2>
            <p>这是一个测试内容。请点击工具栏中的全屏按钮来测试全屏模式。</p>
            <p><strong>测试要点：</strong></p>
            <ul>
                <li>全屏模式下工具栏是否可见</li>
                <li>菜单栏是否正常显示</li>
                <li>状态栏是否在底部显示</li>
                <li>编辑功能是否正常</li>
            </ul>
        </textarea>
    </div>

    <!-- TinyMCE 脚本 -->
    <script src="tinymce.min.js"></script>
    <script src="tinymce-fullscreen-fix.js"></script>
    
    <script>
        tinymce.init({
            selector: '#tinymce-test',
            height: 400,
            menubar: true,
            toolbar_sticky: false, // 关键：禁用粘性工具栏
            plugins: 'fullscreen menubar toolbar lists link image table code preview',
            toolbar: 'undo redo | formatselect | bold italic underline | alignleft aligncenter alignright | bullist numlist | link image | fullscreen | code preview',
            
            // 全屏模式配置
            fullscreen_new_window: false,
            fullscreen_settings: {
                theme_advanced_path_location: "top",
                theme_advanced_toolbar_location: "top"
            },
            
            setup: function (editor) {
                console.log('TinyMCE 编辑器初始化完成');
                
                // 监听全屏状态变化
                editor.on('FullscreenStateChanged', function (e) {
                    console.log('全屏状态变化:', e.state ? '进入全屏' : '退出全屏');
                    
                    if (e.state) {
                        // 进入全屏模式，触发修复
                        setTimeout(function() {
                            if (window.fixTinyMCEFullscreen) {
                                window.fixTinyMCEFullscreen();
                            }
                        }, 100);
                    }
                });
                
                editor.on('init', function() {
                    console.log('编辑器初始化完成，可以开始测试全屏模式');
                });
            }
        });
    </script>
</body>
</html>
