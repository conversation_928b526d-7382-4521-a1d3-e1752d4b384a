# TinyMCE 5.4.1 全屏模式工具栏修复方案

## 🎯 问题描述

TinyMCE 5.4.1 版本在全屏模式下存在已知 bug：
- 工具栏和菜单栏在全屏模式下消失或不可见
- `toolbar_sticky: true` 配置与全屏模式冲突
- 全屏模式下的布局结构异常

## 🔍 根本原因分析

1. **版本 Bug**：TinyMCE 5.4.1 的粘性工具栏与全屏模式存在冲突
2. **CSS 样式问题**：全屏模式下缺少正确的 flexbox 布局
3. **事件处理缺失**：没有正确监听和处理全屏状态变化
4. **配置冲突**：重复的 toolbar 配置导致渲染异常

## ✅ 完整解决方案

### 1. 配置修复

在 TinyMCE 初始化配置中：

```javascript
tinymce.init({
    // 关键修复：禁用粘性工具栏
    toolbar_sticky: false,
    
    // 全屏模式配置
    fullscreen_new_window: false,
    fullscreen_settings: {
        theme_advanced_path_location: "top",
        theme_advanced_toolbar_location: "top"
    },
    
    // 移除重复的 toolbar 配置
    // 确保只有一个 toolbar 设置
    
    setup: function (editor) {
        // 监听全屏状态变化
        editor.on('FullscreenStateChanged', function (e) {
            if (e.state) {
                // 进入全屏模式，触发修复
                setTimeout(function() {
                    if (window.fixTinyMCEFullscreen) {
                        window.fixTinyMCEFullscreen();
                    }
                }, 100);
            }
        });
    }
});
```

### 2. CSS 样式修复

添加专用的全屏模式样式：

```css
/* 全屏模式基础布局 */
.tox-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 10000 !important;
    background: #fff !important;
    display: flex !important;
    flex-direction: column !important;
}

/* 头部区域（菜单栏 + 工具栏） */
.tox-fullscreen .tox-editor-header {
    flex-shrink: 0 !important;
    background: #f4f4f4 !important;
    border-bottom: 1px solid #ccc !important;
}

/* 菜单栏和工具栏 */
.tox-fullscreen .tox-menubar,
.tox-fullscreen .tox-toolbar {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    background: #f4f4f4 !important;
    border-bottom: 1px solid #ddd !important;
}

/* 编辑区域 - 占据剩余空间 */
.tox-fullscreen .tox-edit-area {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
}

.tox-fullscreen .tox-edit-area iframe {
    flex: 1 !important;
    width: 100% !important;
    height: 100% !important;
}

/* 状态栏 - 底部显示 */
.tox-fullscreen .tox-statusbar {
    flex-shrink: 0 !important;
    background: #f4f4f4 !important;
    border-top: 1px solid #ccc !important;
}
```

### 3. JavaScript 修复脚本

`tinymce-fullscreen-fix.js` 提供了：
- 自动样式注入
- 全屏状态监听
- 多层次修复确保
- 强制布局修正

### 4. 文件结构

```
h5/
├── wechat_article_detail.html          # 主页面（已修复）
├── tinymce-fullscreen-fix.js           # 全屏修复脚本
├── tinymce-fullscreen-test.html        # 测试页面
└── TINYMCE_FULLSCREEN_FIX_README.md    # 说明文档
```

## 🧪 测试方法

1. 打开 `tinymce-fullscreen-test.html`
2. 点击编辑器工具栏中的全屏按钮（📺 图标）
3. 检查是否能看到：
   - 顶部菜单栏（文件、编辑、视图等）
   - 工具栏（加粗、斜体、对齐等按钮）
   - 底部状态栏（字数统计等）
4. 测试工具栏功能是否正常
5. 按 ESC 键退出全屏模式

## 🔧 关键修复点

1. **禁用粘性工具栏**：`toolbar_sticky: false`
2. **Flexbox 布局**：使用 flex 布局确保正确的空间分配
3. **事件监听**：监听 `FullscreenStateChanged` 事件
4. **多次修复**：使用 setTimeout 确保 DOM 更新后再修复
5. **强制样式**：使用 `!important` 覆盖默认样式

## 📋 兼容性说明

- ✅ TinyMCE 5.4.1
- ✅ 现代浏览器（Chrome, Firefox, Safari, Edge）
- ✅ 移动端浏览器
- ⚠️ IE11 需要额外的 flexbox polyfill

## 🚀 部署说明

1. 确保 `tinymce-fullscreen-fix.js` 在 TinyMCE 主文件之后加载
2. 在页面 `<head>` 中添加修复样式
3. 在 TinyMCE 配置中设置 `toolbar_sticky: false`
4. 在 `setup` 函数中添加全屏事件监听

## 🔍 故障排除

如果全屏模式仍有问题：

1. 检查浏览器控制台是否有错误
2. 确认 `tinymce-fullscreen-fix.js` 已正确加载
3. 验证 CSS 样式是否被其他样式覆盖
4. 手动调用 `window.fixTinyMCEFullscreen()` 进行测试

## 📞 技术支持

如遇问题，请检查：
- TinyMCE 版本是否为 5.4.1
- 是否正确禁用了 `toolbar_sticky`
- 修复脚本是否在正确时机加载
- CSS 样式是否被正确应用
